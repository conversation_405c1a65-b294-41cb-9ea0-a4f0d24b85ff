# Custom Story Index Implementation

## Overview
The ModifyStoryPreview component now supports custom starting index based on the `isRender` flag in story data.

## How it works

### Logic:
1. If all stories in `currentUser[dataKey]` have `isRender: true` (or no `isRender` flag), start from index 0
2. If any story has `isRender: false`, start from the first story where `isRender` is false

### Example Data Structure:

```javascript
// Example 1: All stories rendered - starts at index 0
const currentUser = {
  Stories: [
    { id: 1, isRender: true, mediaLink: "story1.jpg" },
    { id: 2, isRender: true, mediaLink: "story2.jpg" },
    { id: 3, isRender: true, mediaLink: "story3.jpg" }
  ]
};
// Result: customStartIndex = 0

// Example 2: Some stories not rendered - starts at first false
const currentUser = {
  Stories: [
    { id: 1, isRender: true, mediaLink: "story1.jpg" },
    { id: 2, isRender: false, mediaLink: "story2.jpg" }, // Start here
    { id: 3, isRender: false, mediaLink: "story3.jpg" }
  ]
};
// Result: customStartIndex = 1

// Example 3: No isRender flags - starts at index 0
const currentUser = {
  Stories: [
    { id: 1, mediaLink: "story1.jpg" },
    { id: 2, mediaLink: "story2.jpg" },
    { id: 3, mediaLink: "story3.jpg" }
  ]
};
// Result: customStartIndex = 0
```

## Implementation Details

### Key Changes:
1. Added `findCustomStartIndex()` function to calculate the starting index
2. Added `customStartIndex` state to track the calculated index
3. Modified the Stories component to use `currentIndex={customStartIndex}`
4. Updated the component key to include customStartIndex for proper re-rendering

### Console Logging:
The implementation includes console logging to help debug:
```javascript
console.log("Custom start index calculation:", {
  stories: stories.map(s => ({ id: s?.id, isRender: s?.isRender })),
  firstUnrenderedIndex,
  startIndex
});
```

## Usage
No changes needed in parent components. The logic automatically detects the `isRender` flag in your story data and adjusts the starting index accordingly.

## Testing
To test this functionality:
1. Ensure your story data includes the `isRender` property
2. Open browser console to see the calculation logs
3. Verify that stories start from the correct index based on the `isRender` flags
