"use client";
import { useContext, useEffect, useRef, useState } from "react";
import Image from "next/image";
import {
  base64ToFile,
  blurDataURL,
  isObjEmpty,
  RESPONSE_STATUS,
  stories,
} from "@/utils/function";
import {
  CoverImageIcon,
  EditPencilIcon,
  LeftArrowBackIcon,
  LocationIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import { CustomContainer } from "../Common/Custom-Display";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Post from "./Tabs/Post";
import Projects from "./Tabs/Project";
import Drafts from "./Tabs/Drafts";
import Bookmarks from "./Tabs/Bookmarks";
import FormModal from "../HomePage/FormModal";
import ProfileSkeleton from "../Loader/ProfileSkeleton";
import EditProfileModal from "./EditProfileModal";
import FollowingAndFollowersModal from "./FollowingAndFollowersModal";
import SeeMoreText from "../Common/SeeMoreText";
import { Plus, RotateCcw, RotateCw, Trash2 } from "lucide-react";
import ShowMenuList from "../Common/ShowMenuList";
import {
  followUser,
  getAllHighlights,
  getMyProfile,
  getOneUser,
  getOneUserCurrentStories,
  unFollowUser,
  updateImageToURL,
  updateMyProfile,
} from "@/app/action";
import toast from "react-hot-toast";
import authStorage from "@/utils/API/AuthStorage";
import Empty from "../Common/Empty";
import Modal from "react-responsive-modal";
import AvatarEditor from "react-avatar-editor";
import CustomButton from "../Common/Custom-Button";
import PopUpModal from "../Common/PopUpModal";
import useApiRequest from "../helper/hook/useApiRequest";
import { usePathname, useSearchParams } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import "react-responsive-modal/styles.css";
import { ProfileContext } from "../context/ProfileContext";
import HighlightsList from "../Common/HighlightsList";
import { safeToast } from "@/utils/safeToast";

import tempCoverImg from "../../../public/images/assets/banner.png";
import noUserImg from "../../../public/images/assets/not_person.png";
import AddHighlightModal from "./AddHighlightModal";
import ModifyStoryPreview from "../HomePage/ModifyStoryPreview";

const ProfilePage = ({
  userIdSlug = null,
  children,
  data,
  profileTabs = [
    {
      label: "Posts",
      value: "post",
    },
    {
      label: "Projects",
      value: "projects",
    },
    {
      label: "Notes",
      value: "drafts",
    },
    {
      label: "Bookmarks",
      value: "bookmarks",
    },
  ],
}) => {
  const [profileData, setProfileData] = useState(null);
  const [activeTab, setActiveTab] = useState("post");
  const [isOpen, setIsOpen] = useState(false);
  const [dataList, setDataList] = useState([]);
  const [editData, setEditData] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [type, setType] = useState({
    isOpen: false,
    type: null,
  });
  const [refresh, setRefresh] = useState(undefined);
  const [isFollow, setIsFollow] = useState(false);
  const [loginUserData, setLoginUserData] = useState(null);
  // For Fetching The use Data
  const [isDataFetching, setIsDataFetching] = useState(true);
  // Image Crop
  const [selectedImage, setSelectedImage] = useState(null);
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [showCropModal, setShowCropModal] = useState(false);
  const [highlights, setHighlights] = useState([]);
  const [isHighlighAdd, setIsHighlighAdd] = useState(false);
  // For the Cover Image
  const [isLoading, setIsLoading] = useState(false);
  const [deleteImage, setDeleteImage] = useState(false);
  const coverImageInputRef = useRef(null);
  const editorRef = useRef(null);
  const imageAPI = useApiRequest(false);
  const api = useApiRequest(!data);
  const router = useRouter();
  const path = userIdSlug
    ? usePathname().split("/")?.[3]
    : usePathname().split("/")?.[2];
  // console.log(profileData, "User data or Profile Data");

  const [storyList, setStoryList] = useState([]);
  const [isStoryOpen, setIsStoryOpen] = useState(false);

  console.log(storyList);

  const storyAPI = useApiRequest(false);

  const { setProfile, isMobile } = useContext(ProfileContext);

  const resetCoverImageModal = () => {
    setShowCropModal(false);
    setSelectedImage(null);
    setScale(1);
    setRotate(0);
  };

  // Data List Reset
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const uploadCoverImageHandler = (payload) => {
    // console.log(payload);
    imageAPI.sendRequest(
      updateMyProfile,
      (res) => {
        // setRefresh((prev) => !prev);
        setRefresh((prev) => (typeof prev === "undefined" ? true : !prev));
        resetDataList();
        resetCoverImageModal();
        setIsLoading(false);
        setDeleteImage(false);
      },
      payload,
      "",
      () => {
        setIsLoading(false);
      }
    );
  };

  // Save Cover Image
  const handleSave = async () => {
    if (editorRef.current) {
      setIsLoading(true);
      const canvas = editorRef.current.getImageScaledToCanvas();
      const croppedImage = canvas.toDataURL(); // base64 image
      // console.log(canvas, croppedImage);
      let payload = {};
      const finalImage = base64ToFile(croppedImage, "image");

      const formData = new FormData();
      formData.append("file", finalImage);

      try {
        const res = await updateImageToURL(formData);
        // setSelectedImage(res?.data?.[0]?.link);
        payload = {
          backgroundImage: res?.data?.[0]?.link,
        };
      } catch (error) {
        console.dir(error);
        return;
      }

      uploadCoverImageHandler(payload);

      // console.log(finalImage);
    }
  };

  //
  // Image OnChange Preview Image
  const handleProfileImage = (e) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }

    const file = e.target.files?.[0];
    // console.log(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setSelectedImage(reader.result);
        setShowCropModal(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Setting Options
  const profileOption = [
    {
      label: "Upload a Cover Photo",
      className: "",
      icon: <CoverImageIcon size={18} />,
      onClick: () => {
        coverImageInputRef.current?.click();
      },
    },
    {
      label: "Remove",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: () => {
        // console.log(ele);
        setDeleteImage(true);
      },
    },
  ];

  // Follow and Following when this page works for User
  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      // console.log(response);
      // safeToast.success(response?.message);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };

  // Get Profile Data
  useEffect(() => {
    setIsDataFetching(true);
    const existingUser = authStorage.getProfileDetails();
    if (existingUser && Object.keys(existingUser).length > 0) {
      setLoginUserData(existingUser);
    }
    if (typeof refresh === "boolean") {
      let GET_ME_API = {};
      if (userIdSlug) {
        GET_ME_API = getOneUser;
      } else {
        GET_ME_API = getMyProfile;
      }
      api.sendRequest(
        GET_ME_API,
        (res) => {
          const responseData = res?.data;
          setProfile(responseData);
          setProfileData(responseData);
          if (userIdSlug) {
            setIsFollow(+responseData?.isFollowed === 1);
          }
        },
        userIdSlug
      );
    } else {
      if (data && Object.keys(data).length > 0) {
        setProfileData(data?.data);
        if (userIdSlug) {
          setIsFollow(+data?.data?.isFollowed === 1);
        }
      }
    }
    setIsDataFetching(false);
  }, [refresh, data]);

  const headerRef = useRef(null);

  // Get User Story
  useEffect(() => {
    if (profileData) {
      storyAPI.sendRequest(
        getOneUserCurrentStories,
        (res) => {
          setStoryList(res?.data);
        },
        {
          id: profileData?.id,
        },
        "",
        (err) => {
          console.log(err);
        }
      );
    }
  }, [profileData]);

  useEffect(() => {
    if (headerRef.current) {
      // headerRef.current.scrollIntoView({
      //   behavior: "smooth",
      //   block: "start",
      // });
      headerRef.current.scrollTo(0, 0);
    }
    window.scrollTo(0, 0);
  }, [userIdSlug]);

  // Get Highlights
  useEffect(() => {
    if (profileData?.id) {
      api.sendRequest(
        getAllHighlights,
        (res) => {
          setHighlights(res?.data?.data);
        },
        {
          UserId: profileData?.id,
        }
      );
    }
  }, [profileData]);

  return (
    <div ref={headerRef}>
      {/* Story Preview */}
      <ModifyStoryPreview
        isOpen={isStoryOpen}
        setIsOpen={setIsStoryOpen}
        currentUserIndex={0}
        // setCurrentUserIndex={setCurrentUserIndex}
        storyData={storyList}
        loginUserData={loginUserData}
        setStoryData={setStoryList}
      />
      {/* Delete Popup */}
      <PopUpModal
        isLoading={imageAPI.isLoading}
        isOpen={deleteImage}
        setIsOpen={setDeleteImage}
        mainMessage="Delete Cover Image"
        subMessage="Are you sure you want to Delete Cover Image Permanently?"
        onConfirm={() => {
          const payload = {
            backgroundImage: null,
          };
          uploadCoverImageHandler(payload);
        }}
      />

      {/* Add Highlight Modal */}
      <AddHighlightModal
        isOpen={isHighlighAdd}
        setIsOpen={setIsHighlighAdd}
        setRefresh={setRefresh}
        userId={profileData?.id}
      />
      {/* Crop Modal */}
      {showCropModal && selectedImage && (
        <Modal
          open={showCropModal}
          onClose={() => {
            if (!isLoading) {
              resetCoverImageModal();
            }
          }}
          classNames={{
            modal:
              "!tw-max-w-[100%] !tw-m-0 md:!tw-m-[1.2rem] lg:!tw-w-[65rem] !tw-rounded-[1.25rem] !tw-mt-1",
            closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
            overlay: "!tw-bg-[#000000CC]",
          }}
          focusTrapped={false}
          center
        >
          <h2 className="tw-text-2xl  tw-font-semibold">Cover Image</h2>
          {selectedImage && (
            <>
              <div className="tw-flex tw-justify-center tw-pt-4 ">
                <AvatarEditor
                  className="!tw-rounded-xl"
                  ref={editorRef}
                  image={selectedImage}
                  width={isMobile ? 200 : 800}
                  height={isMobile ? 200 : 340}
                  // width={250}
                  // height={250}
                  border={30}
                  color={[255, 255, 255, 0.7]} // RGBA
                  scale={scale}
                  rotate={rotate}
                  crossOrigin="anonymous"
                  // showGrid={true}
                  borderRadius={10}
                />
              </div>
              <div className="tw-my-4 tw-flex tw-justify-center tw-gap-7">
                <div className="tw-flex tw-gap-2 tw-items-center tw-text-xl">
                  <label>Zoom: </label>
                  <input
                    type="range"
                    min="1"
                    max="3"
                    step="0.01"
                    value={scale}
                    onChange={(e) => setScale(parseFloat(e.target.value))}
                  />
                </div>
                <div className="tw-flex tw-gap-2 tw-items-center tw-text-xl">
                  <label>Rotate: </label>

                  <div className="tw-flex tw-items-center tw-gap-3">
                    <button
                      type="button"
                      className="tw-outline-none"
                      onClick={() => {
                        setRotate((prev) => (prev - 90 + 360) % 360);
                      }}
                    >
                      <RotateCcw size={20} />
                    </button>
                    <button
                      className="tw-outline-none"
                      type="button"
                      onClick={() => {
                        setRotate((prev) => (prev + 90) % 360);
                      }}
                    >
                      <RotateCw size={20} />
                    </button>
                  </div>
                </div>
              </div>
              <div className="tw-flex tw-justify-center tw-gap-3 tw-py-3">
                <CustomButton
                  // loading={isLoading}
                  disabled={isLoading}
                  className={`!tw-px-9 !tw-py-[14px] tw-bg-transparent !tw-text-primary-black ${
                    isLoading && "tw-cursor-not-allowed"
                  }`}
                  onClick={() => {
                    resetCoverImageModal();
                  }}
                  type="submit"
                  count={8}
                >
                  Cancel
                </CustomButton>
                <CustomButton
                  loading={isLoading}
                  disabled={isLoading}
                  className={`!tw-px-9 !tw-py-[14px] ${
                    isLoading && "tw-cursor-not-allowed"
                  }`}
                  type="submit"
                  count={8}
                  onClick={() => {
                    handleSave();
                  }}
                >
                  Save
                </CustomButton>
              </div>
            </>
          )}
        </Modal>
      )}

      {/* Cover Image */}
      <input
        type="file"
        accept="image/*"
        ref={coverImageInputRef}
        className="tw-hidden"
        onChange={handleProfileImage}
      />
      {/* Followers and Following Modal */}
      <FollowingAndFollowersModal
        setRefresh={setRefresh}
        modalType={type}
        setModalType={setType}
        UserId={userIdSlug && profileData?.id}
        loginUserData={authStorage.getProfileDetails() ?? null}
      />
      {/* Create Post,Project Modal */}
      <FormModal
        reFetchData={resetDataList}
        setRefresh={setRefresh}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
      />
      {api.isLoading || isDataFetching ? (
        <ProfileSkeleton />
      ) : !profileData ? (
        // <div className="tw-h-[35rem] tw-flex tw-justify-center tw-items-center">
        //   <Empty />
        //   </div>
        <div className="tw-h-[80dvh] tw-flex tw-justify-center tw-items-center">
          <Empty
            icon={<Image src={noUserImg} width={261} height={248} />}
            iconRequired={userIdSlug}
            label={userIdSlug ? "User Not Found" : "No Record Found"}
          />
        </div>
      ) : (
        <>
          <EditProfileModal
            setRefresh={setRefresh}
            resetDataList={resetDataList}
            formData={{ ...profileData }}
            open={isModalOpen}
            setIsOpen={setIsModalOpen}
          />
          <CustomContainer className="tw-pt-4">
            <button
              type="button"
              onClick={() => {
                //

                router.back();
              }}
              className="tw-mb-4"
            >
              <LeftArrowBackIcon />
            </button>
            <div className="tw-relative tw-w-full tw-h-[20rem] ">
              <Image
                src={profileData?.backgroundImage ?? tempCoverImg}
                alt={"cover image"}
                fill
                className="tw-rounded-3xl tw-object-cover"
                placeholder="blur"
                //   priority
                blurDataURL={blurDataURL(800, 400)}
              />
              <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
              {!userIdSlug && (
                <ShowMenuList
                  data={profileData}
                  menuList={profileOption?.filter((ele) =>
                    profileData?.backgroundImage ? ele : ele?.label !== "Remove"
                  )}
                >
                  <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-7">
                    <ThreeDotMenuIcon fill="#fff" size={24} />
                  </div>
                </ShowMenuList>
              )}
              <div
                className={`tw-absolute tw-top-[77%] tw-left-[33%] 425:tw-left-[35%] md:tw-left-[40%] lg:tw-left-[44%] 2xl:tw-left-[45%] ${
                  storyList?.length > 0 ? "tw-cursor-pointer" : ""
                }`}
                onClick={() => {
                  if (storyList?.length > 0) {
                    setIsStoryOpen(true);
                  }
                }}
              >
                <div
                  className={`tw-relative tw-w-[8rem]  tw-h-[8rem] tw-rounded-full tw-border-[5px] ${
                    storyList?.length > 0
                      ? storyList?.[0]?.Stories?.every(
                          (ele) => ele?.isViewByCurrentUser
                        )
                        ? "tw-border-[#CCCCCC]"
                        : " tw-border-primary-purple"
                      : "tw-border-white"
                  } ${
                    !profileData?.image && !api.isLoading
                      ? "tw-flex tw-justify-center tw-items-center tw-bg-primary-purple tw-text-white"
                      : "tw-bg-white"
                  }`}
                >
                  {profileData?.image ? (
                    <Image
                      src={profileData?.image}
                      className="!tw-rounded-full !tw-object-cover"
                      alt="profile"
                      placeholder="blur"
                      priority
                      fill
                      blurDataURL={blurDataURL(300, 300)}
                    />
                  ) : (
                    <span className="tw-text-primary-1100 tw-font-merriweather tw-font-medium tw-text-6xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                      {profileData?.firstName?.charAt(0)}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Profile Details */}
            <div className="tw-flex tw-flex-col tw-gap-3 tw-items-center tw-mt-16">
              {/* Name */}
              <p className="tw-text-primary-black tw-font-semibold tw-text-3xl">{`${profileData?.firstName} ${profileData?.lastName}`}</p>
              {/* Email */}
              {/* {!userIdSlug && (
                <p className="tw-text-[#787E89] -tw-mt-2.5">
                  {profileData?.email}
                </p>
              )} */}
              {/* Location */}

              {profileData?.location && (
                <div className="tw-text-[#787E89] tw-flex tw-gap-1.5 tw-text-sm lg:tw-text-base tw-items-start lg:tw-items-center">
                  <div className="tw-relative tw-top-1 md:tw-top-0">
                    <LocationIcon />
                  </div>
                  <p className="tw-max-w-[22rem] md:tw-max-w-[35rem] lg:tw-max-w-[100%]">
                    {profileData?.location}
                  </p>
                </div>
              )}

              {/* Followers and Following Count */}
              <div className="tw-flex tw-gap-3 tw-justify-between tw-items-center tw-text-primary-black tw-font-bold">
                <button
                  type="button"
                  className={``}
                  onClick={() => {
                    // if (!userIdSlug) {
                    setType({
                      isOpen: true,
                      type: "followerUser",
                    });
                    // }
                  }}
                >
                  {+profileData?.followersCount ?? 0} followers
                </button>
                <div className="tw-h-1 tw-w-1 tw-rounded-full tw-bg-primary-black" />
                <button
                  type="button"
                  className={``}
                  onClick={() => {
                    // if (!userIdSlug) {
                    setType({
                      isOpen: true,
                      type: "followingUser",
                    });
                    // }
                  }}
                >
                  {+profileData?.followingsCount ?? 0} following
                </button>
              </div>
              {/* About */}
              <SeeMoreText
                text={profileData?.about}
                fontWeight="400"
                fontSize={16}
                color="black"
                maxLines={2}
                textAlign="left"
                className={
                  "tw-mx-auto tw-max-w-[32.8rem]  tw-text-primary-black"
                }
              />
              {/* Edit Profile */}
              {userIdSlug && loginUserData?.id !== profileData?.id ? (
                <button
                  onClick={() => {
                    // followAndUnfollowProjectHandler(projectData?.id, !isFollow);

                    if (isFollow) {
                      //
                      followAndUnfollowHandler(profileData?.id, false);
                    } else {
                      followAndUnfollowHandler(profileData?.id, true);
                    }
                    setIsFollow((prev) => !prev);
                  }}
                  className={`${
                    isFollow
                      ? "tw-bg-transparent tw-border tw-border-[#787E89] tw-text-[#787E89]"
                      : "tw-bg-primary-purple tw-text-white tw-border tw-border-primary-purple"
                  }  tw-text-lg tw-py-4 tw-px-7 tw-rounded-full tw-font-semibold`}
                >
                  {isFollow ? "Following" : "Follow"}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={() => setIsModalOpen(true)}
                  className="tw-bg-transparent  tw-border tw-border-[#787E89] tw-text-primary-black tw-text-sm tw-py-4 tw-px-7 tw-rounded-full tw-font-semibold"
                >
                  Edit Profile
                </button>
              )}
            </div>

            {/* Highlights */}

            {(highlights?.length > 0 ||
              (!userIdSlug && loginUserData?.id === profileData?.id)) && (
              <div className="tw-w-full tw-overflow-hidden tw-block tw-mt-8">
                {/* Add Highlight */}
                <div className="tw-flex tw-items-start tw-gap-3">
                  {!userIdSlug && loginUserData?.id === profileData?.id && (
                    <div className="tw-flex tw-flex-col tw-items-center">
                      <button
                        type="button"
                        onClick={() => {
                          setIsHighlighAdd(true);
                        }}
                        className="tw-relative tw-w-[5.3rem] tw-h-[5.3rem] tw-flex tw-items-center tw-justify-center"
                      >
                        {/* Dashed Circle via SVG */}
                        <svg
                          className="tw-absolute tw-w-full tw-h-full"
                          viewBox="0 0 100 100"
                        >
                          <circle
                            cx="50"
                            cy="50"
                            r="48"
                            fill="#f3e8ff" // Tailwind: tw-bg-purple-100
                            stroke="#a855f7" // Tailwind: tw-border-purple-500
                            strokeWidth="2"
                            strokeDasharray="10 8"
                          />
                        </svg>

                        {/* Inner Button */}
                        <div className="tw-w-10 tw-h-10 tw-rounded-full tw-bg-white tw-border tw-border-gray-700 tw-flex tw-items-center tw-justify-center tw-z-10">
                          <Plus className="tw-text-gray-700" />
                        </div>
                      </button>
                      <p className="tw-text-sm tw-w-full tw-font-bold tw-text-primary-black tw-pt-1">
                        Create New
                      </p>
                    </div>
                  )}

                  {highlights?.length > 0 && (
                    <HighlightsList
                      data={highlights}
                      setReload={setRefresh}
                      isSettingVisible={loginUserData?.id === profileData?.id}
                    />
                  )}
                </div>
              </div>
            )}

            {/* Profile Tab */}

            <div className="tw-flex tw-justify-between tw-items-center tw-overflow-auto">
              <div className="tw-flex  tw-items-center tw-gap-4 tw-my-10">
                {profileTabs?.map((ele) => (
                  <button
                    type="button"
                    onClick={() => {
                      // setActiveTab(ele?.value);
                      // resetDataList();

                      const baseRoute = userIdSlug
                        ? `/user/${userIdSlug}`
                        : "/profile";
                      router.push(
                        ele?.value === "post"
                          ? baseRoute
                          : `${baseRoute}/${ele?.value}`
                      );
                    }}
                    key={ele?.label}
                    className={`tw-py-3 tw-cursor-pointer tw-font-medium tw-rounded-full ${
                      ele?.value === (path ?? "post")
                        ? "!tw-font-bold tw-bg-[#F6EFFE] tw-text-incenti-purple"
                        : "tw-bg-[#F5F7F8] tw-text-[#2D394A] "
                    } tw-px-5`}
                  >
                    {ele?.label}
                  </button>
                ))}
              </div>
            </div>

            <div>{children}</div>
          </CustomContainer>
        </>
      )}
    </div>
  );
};

export default ProfilePage;
